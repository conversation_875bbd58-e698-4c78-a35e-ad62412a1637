<template>
  <div class="page">
    <router-view v-if="$route.params.id"></router-view>
    <div class="page-content" v-else>
      <div
        v-if="loading"
        class="loading-container"
        v-loading="loading"
        element-loading-background="rgba(255, 255, 255, 0.7)"
        element-loading-text="加载知识库中..."
      >
        <!-- 移除el-loading组件，改用v-loading指令 -->
      </div>
      <div v-else class="main-container">
        <!-- 页面头部 -->
        <div class="page-header">
          <span class="page-title">知识库</span>
          <div class="header-info">
            <span class="capacity-info">剩余容量：37.5mb</span>
            <span class="expand-link">扩容</span>
          </div>
          <div class="create-btn" @click="handleCreateKnowledge">
            <i class="create-icon">+</i>
            <span class="create-text">新建知识库</span>
          </div>
        </div>

        <!-- 搜索框 -->
        <div class="search-container">
          <div class="search-box">
            <i class="search-icon"></i>
            <el-input
              v-model="searchKeyword"
              placeholder="搜索知识库"
              clearable
              @input="handleSearchChange"
              class="search-input"
            ></el-input>
          </div>
        </div>

        <!-- 知识库卡片瀑布流 -->
        <div class="knowledge-grid">
          <div
            v-for="knowledge in filteredKnowledgeList"
            :key="knowledge.id"
            class="knowledge-card"
            @click="handleSettingClick(knowledge)"
          >
            <!-- 卡片头部 -->
            <div class="card-header">
              <div class="knowledge-avatar"></div>
              <div class="knowledge-info">
                <div class="knowledge-title">{{ knowledge.name }}</div>
                <div class="knowledge-description">
                  {{ knowledge.description || "暂无描述" }}
                </div>
              </div>
            </div>

            <!-- 应用引用信息 -->
            <div class="app-reference">应用引用 3</div>

            <!-- 分割线 -->
            <div class="divider"></div>

            <!-- 卡片底部 -->
            <div class="card-footer">
              <span class="update-time">最近更新 {{ formatTime(knowledge.updateTime) }}</span>
              <div class="edit-btn" @click.stop="handleSettingClick(knowledge)">
                <span class="edit-text">编辑</span>
              </div>
              <div class="more-actions">
                <div class="action-dots"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 创建知识库弹窗 -->
    <el-dialog
      title="创建知识库"
      :visible.sync="createDialogVisible"
      width="600px"
      :close-on-click-modal="false"
      append-to-body
      @close="handleDialogClose"
    >
      <el-form
        :model="createForm"
        ref="createForm"
        :rules="rules"
        label-position="top"
        @submit.native.prevent
      >
        <el-form-item label="知识库名称" prop="name" class="form-item">
          <el-input
            v-model="createForm.name"
            placeholder="给你的知识库取一个名字吧"
            maxlength="20"
            show-word-limit
          ></el-input>
        </el-form-item>

        <el-form-item label="知识库描述" prop="description" class="form-item">
          <el-input
            type="textarea"
            v-model="createForm.description"
            placeholder="请输入知识库描述"
            :rows="4"
          ></el-input>
        </el-form-item>

        <div class="dialog-tip">
          <i class="el-icon-info"></i>
          知识库需绑定到知识智能体才可生效
        </div>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleDialogClose">取消</el-button>
        <el-button type="primary" @click="handleCreateConfirm">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { api } from "@/api/request";
import EventBus from "@/utils/eventBus";

export default {
  name: "CreateKnowledge",
  data() {
    return {
      loading: true,
      createDialogVisible: false,
      searchKeyword: "",
      createForm: {
        name: "",
        description: "",
        userid: "",
      },
      rules: {
        name: [
          { required: true, message: "请输入知识库名称", trigger: "blur" },
          { max: 20, message: "长度不能超过20个字符", trigger: "blur" },
        ],
      },
      knowledgeList: [],
    };
  },
  computed: {
    // 过滤知识库列表
    filteredKnowledgeList() {
      if (!this.searchKeyword) {
        return this.knowledgeList;
      }
      const keyword = this.searchKeyword.toLowerCase();
      return this.knowledgeList.filter(item =>
        item.name.toLowerCase().includes(keyword) ||
        (item.description && item.description.toLowerCase().includes(keyword))
      );
    }
  },
  created() {
    this.fetchKnowledgeList();
    // 监听刷新事件
    EventBus.$on("refresh-knowledge-list", this.fetchKnowledgeList);
  },
  beforeDestroy() {
    // 组件销毁前移除事件监听
    EventBus.$off("refresh-knowledge-list", this.fetchKnowledgeList);
  },
  methods: {
    fetchKnowledgeList() {
      // 加载状态
      this.loading = true;

      // 查询参数 - 对于GET请求，这些会作为URL参数传递
      const params = {
        keyword: "",
        pageIndex: 0,
        pageSize: 100,
      };

      // 调用API获取知识库列表
      api.rag
        .getKnowledgeList(params)
        .then((res) => {
          if (res.code === 200) {
            // 处理返回数据
            if (res.data && res.data.items && res.data.items.length > 0) {
              // 将API返回的数据映射为组件需要的结构
              this.knowledgeList = res.data.items.map((item) => ({
                id: item.id,
                name: item.name,
                description: item.description,
                code: item.code,
                createTime: item.create_time,
                updateTime: item.update_time,
              }));
            } else {
              this.knowledgeList = []; // 没有数据时设为空数组
            }
          } else {
            this.$showFriendlyError({ message: res.status?.message }, "获取知识库列表失败");
            // 保持默认示例数据用于展示
          }
        })
        .catch((err) => {
          this.$showFriendlyError(err, "获取知识库列表失败，请重试");
          // 保持默认示例数据用于展示
        })
        .finally(() => {
          this.loading = false;
        });
    },
    handleCreateKnowledge() {
      this.createDialogVisible = true;
    },
    handleSettingClick(knowledge) {
      this.$router.push(`/create/knowledge/${knowledge.id}/setting`);
    },
    handleDialogClose() {
      this.createDialogVisible = false;
      this.$refs.createForm?.resetFields();
    },
    handleCreateConfirm() {
      this.$refs.createForm.validate((valid) => {
        if (valid) {
          // 创建知识库请求参数
          const params = {
            name: this.createForm.name,
            description: this.createForm.description,
          };

          // 发起创建知识库请求
          this.loading = true;
          api.rag
            .createKnowledge(params)
            .then((res) => {
              if (res.isSuccess || res.success) {
                this.$message.success("知识库创建成功");
                // 刷新知识库列表
                this.fetchKnowledgeList();
              } else {
                this.$showFriendlyError({ message: res.message }, "创建知识库失败");
              }
            })
            .catch((err) => {
              this.$showFriendlyError(null,
                "创建知识库失败：" + (err.message || "未知错误")
              );
            })
            .finally(() => {
              this.loading = false;
              this.createDialogVisible = false;
              this.$refs.createForm.resetFields();
            });
        }
      });
    },
    formatTime(time) {
      if (!time) return "2025-07-10 14:20:30";
      // 这里可以添加时间格式化逻辑
      return "2025-07-10 14:20:30";
    },
    // 处理搜索关键词变化
    handleSearchChange() {
      // 搜索输入变化时，直接通过computed重新计算filteredKnowledgeList
    }
  },
};
</script>

<style lang="scss" scoped>
.page {
  background-color: rgba(242, 246, 252, 1);
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

.page-content {
  width: 100%;
  height: 100%;
}

.main-container {
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  height: calc(100vh);
  border: 0.5px solid rgba(235, 236, 241, 1);
  position: relative;
  overflow-y: auto;
}

.page-header {
  display: flex;
  align-items: center;
  width: calc(100% - 80px);
  height: 36px;
  margin: 30px 0 0 40px;
}

.page-title {
  font-size: 24px;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  color: rgba(0, 0, 0, 1);
  line-height: 33px;
  margin-right: 16px;
}

.header-info {
  display: flex;
  align-items: center;
  margin-right: auto;
}

.capacity-info {
  font-size: 14px;
  color: rgba(102, 102, 102, 1);
  line-height: 20px;
}

.expand-link {
  font-size: 14px;
  color: rgba(37, 109, 255, 1);
  line-height: 20px;
  margin-left: 8px;
  cursor: pointer;
}

.create-btn {
  background-color: rgba(37, 109, 255, 1);
  border-radius: 8px;
  width: 116px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.create-icon {
  color: rgba(255, 255, 255, 1);
  font-size: 14px;
  margin-right: 8px;
  font-style: normal;
}

.create-text {
  color: rgba(255, 255, 255, 1);
  font-size: 14px;
  line-height: 20px;
}

.search-container {
  width: 240px;
  height: 36px;
  margin: 31px 0 0 40px;
}

.search-box {
  background-color: rgba(244, 246, 248, 1);
  border-radius: 8px;
  width: 240px;
  height: 36px;
  display: flex;
  align-items: center;
  padding: 0 13px;
  position: relative;
}

.search-icon {
  width: 15px;
  height: 16px;
  background: url('@/assets/layouts/icon-search.png') no-repeat center;
  background-size: contain;
  margin-right: 12px;
  position: absolute;
  left: 13px;
  z-index: 1;
}

.search-input {
  width: 100%;

  ::v-deep .el-input__inner {
    height: 36px;
    padding-left: 35px;
    background: transparent;
    border: none;
    font-size: 14px;
    color: rgba(186, 186, 186, 1);

    &:focus {
      color: #303133;
    }
  }

  ::v-deep .el-input__inner::placeholder {
    color: rgba(186, 186, 186, 1);
  }
}

.knowledge-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(511px, 1fr));
  gap: 20px;
  width: calc(100% - 80px);
  margin: 32px 0 0 40px;
  padding-bottom: 40px;
}

.knowledge-card {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 8px;
  width: 511px;
  height: 163px;
  border: 0.5px solid rgba(235, 236, 241, 1);
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}

.card-header {
  display: flex;
  align-items: flex-start;
  width: 464px;
  height: 46px;
  margin: 16px 0 0 16px;
}

.knowledge-avatar {
  border-radius: 50%;
  background-image: url('@/assets/knowledge/knowledge.png');
  background-size: cover;
  width: 44px;
  height: 44px;
}

.knowledge-info {
  width: 408px;
  height: 46px;
  margin-left: 12px;
}

.knowledge-title {
  width: 128px;
  height: 22px;
  font-size: 16px;
  font-family: PingFangSC-Semibold;
  font-weight: 600;
  color: rgba(0, 0, 0, 1);
  line-height: 22px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.knowledge-description {
  width: 408px;
  height: 20px;
  font-size: 12px;
  color: rgba(136, 136, 136, 1);
  line-height: 20px;
  margin-top: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.app-reference {
  width: 60px;
  height: 20px;
  font-size: 12px;
  color: rgba(186, 186, 186, 1);
  line-height: 20px;
  margin: 10px 0 0 72px;
}

.divider {
  width: 479px;
  height: 1px;
  background-color: rgba(235, 236, 241, 1);
  margin: 15px 0 0 16px;
}

.card-footer {
  display: flex;
  align-items: center;
  width: 480px;
  height: 30px;
  margin: 12px 0 12px 15px;
}

.update-time {
  width: 173px;
  height: 20px;
  font-size: 12px;
  color: rgba(186, 186, 186, 1);
  line-height: 20px;
  margin-top: 5px;
}

.edit-btn {
  background-color: rgba(244, 246, 248, 1);
  border-radius: 4px;
  height: 30px;
  width: 52px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 203px;
  cursor: pointer;
  transition: all 0.3s;

  &:hover {
    background-color: #e1f3d8;
  }
}

.edit-text {
  width: 28px;
  height: 20px;
  font-size: 14px;
  color: rgba(0, 0, 0, 1);
  line-height: 20px;
}

.more-actions {
  width: 44px;
  height: 30px;
  margin-left: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.action-dots {
  width: 30px;
  height: 30px;
  background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDQiIGhlaWdodD0iMzAiIHZpZXdCb3g9IjAgMCA0NCAzMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTAiIGN5PSIxNSIgcj0iMiIgZmlsbD0iIzk5OTk5OSIvPgo8Y2lyY2xlIGN4PSIyMiIgY3k9IjE1IiByPSIyIiBmaWxsPSIjOTk5OTk5Ii8+CjxjaXJjbGUgY3g9IjM0IiBjeT0iMTUiIHI9IjIiIGZpbGw9IiM5OTk5OTkiLz4KPC9zdmc+Cg==') no-repeat center;
}

.loading-container {
  width: 100%;
  height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;

  .loading-text {
    color: #909399;
    margin-top: 120px;
    font-size: 14px;
  }
}

// 创建知识库弹窗样式
::v-deep .el-dialog {
  border-radius: 8px;

  .el-dialog__header {
    padding: 20px 20px 10px;
    margin-right: 0;
    border-bottom: 1px solid #f0f0f0;
  }

  .el-dialog__body {
    padding: 24px 20px;
  }

  .el-dialog__footer {
    padding: 10px 20px 20px;
    border-top: 1px solid #f0f0f0;
  }
}

.form-item {
  margin-bottom: 24px;

  ::v-deep .el-form-item__label {
    padding-bottom: 8px;
    line-height: 1;
    color: #606266;
  }
}

.dialog-tip {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background-color: #f5f7fa;
  border-radius: 4px;
  color: #909399;
  font-size: 14px;

  i {
    color: var(--el-color-primary);
  }
}
</style>
